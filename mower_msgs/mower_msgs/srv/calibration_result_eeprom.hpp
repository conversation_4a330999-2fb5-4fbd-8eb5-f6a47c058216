#pragma once

#include "camera_bev_params.hpp"
#include "camera_bev_params_2.hpp"
#include "camera_imu_params.hpp"

#include <cstdint>

namespace mower_msgs::srv
{

struct SetCalibReusltEepromRequest
{
    bool bev_calib_result{false};
    bool imu_calib_result{false};
    BEVCalibParams bev_params;
    BEVCalibParams2 bev_params_2;
    IMUCalibParams imu_params;
};

struct SetCalibResultEepromResponse
{
    bool success{false};
};

} // namespace mower_msgs::srv